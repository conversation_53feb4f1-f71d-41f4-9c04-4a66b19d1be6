import React, { useRef, useState } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { GMP_MODULES } from '../data/gmpModules';
import { useGameRedux } from '../store/useGameRedux';
import { Button, DifficultyBadge, StarRating } from '../components/ui';
import { useDeviceLayout } from '../hooks/useOrientation';
import type { Level, Module } from '../types';

import React, { useState, useRef, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface LearningModule {
  id: number;
  title: string;
  step: string;
  level: string;
  bloomsLevel: string;
  duration: string;
  color: string;
  bgGradient: string;
  levelColor: string;
}

const modules: LearningModule[] = [
  {
    id: 1,
    title: "Introduction to GMP",
    step: "1. Recall",
    level: "Beginner",
    bloomsLevel: "Recall",
    duration: "15 min",
    color: "text-pink-100",
    bgGradient: "from-pink-400 via-pink-500 to-rose-500",
    levelColor: "bg-green-500"
  },
  {
    id: 2,
    title: "Regulatory Bodies",
    step: "2. Classify",
    level: "Intermediate",
    bloomsLevel: "Classify",
    duration: "20 min",
    color: "text-orange-100",
    bgGradient: "from-orange-400 via-amber-500 to-yellow-500",
    levelColor: "bg-blue-500"
  },
  {
    id: 3,
    title: "GMP Guidelines",
    step: "3. Apply",
    level: "Advanced",
    bloomsLevel: "Apply",
    duration: "25 min",
    color: "text-teal-100",
    bgGradient: "from-teal-400 via-cyan-500 to-emerald-500",
    levelColor: "bg-red-500"
  },
  {
    id: 4,
    title: "GMP Analysis",
    step: "4. Analyze",
    level: "Expert",
    bloomsLevel: "Analyze",
    duration: "30 min",
    color: "text-purple-100",
    bgGradient: "from-purple-400 via-violet-500 to-indigo-500",
    levelColor: "bg-yellow-500"
  }
];

function LevelList() {
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [currentTranslateX, setCurrentTranslateX] = useState(0);
  const [baseTranslateX, setBaseTranslateX] = useState(0);
  const [velocity, setVelocity] = useState(0);
  const [lastMoveTime, setLastMoveTime] = useState(0);
  const [lastMoveX, setLastMoveX] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const touchStartTimeRef = useRef<number>(0);
  const velocityHistoryRef = useRef<Array<{velocity: number, time: number}>>([]);
  
  // Responsive card dimensions
  const getCardDimensions = () => {
    const isLandscape = window.innerHeight < window.innerWidth && window.innerWidth < 1024;
    const isMobile = window.innerWidth < 768;
    
    if (isLandscape && isMobile) {
      return { width: 240, gap: 16 }; // Smaller for mobile landscape
    } else if (isMobile) {
      return { width: 280, gap: 16 }; // Mobile portrait
    }
    return { width: 320, gap: 20 }; // Desktop
  };

  const { width: cardWidth, gap: cardGap } = getCardDimensions();
  const totalWidth = cardWidth + cardGap;

  const getPositionX = (event: TouchEvent | MouseEvent): number => {
    return 'touches' in event ? event.touches[0].clientX : event.clientX;
  };

  const updateTransform = (translateX: number) => {
    if (containerRef.current) {
      containerRef.current.style.transform = `translateX(${translateX}px)`;
    }
    if (progressRef.current) {
      progressRef.current.style.transform = `translateX(${translateX}px)`;
    }
  };

  const applyBoundaries = (translateX: number) => {
    const maxTranslate = 100;
    const minTranslate = -(modules.length * totalWidth) - 100;
    return Math.max(minTranslate, Math.min(maxTranslate, translateX));
  };

  const calculateAverageVelocity = () => {
    const history = velocityHistoryRef.current;
    if (history.length === 0) return 0;
    
    // Use recent velocity samples for more accurate momentum
    const recentHistory = history.slice(-5);
    const totalVelocity = recentHistory.reduce((sum, item) => sum + item.velocity, 0);
    return totalVelocity / recentHistory.length;
  };

  const startMomentumAnimation = (initialVelocity: number) => {
    // Use average velocity for smoother momentum
    const avgVelocity = calculateAverageVelocity();
    let currentVelocity = Math.abs(avgVelocity) > Math.abs(initialVelocity) ? avgVelocity : initialVelocity;
    
    // Minimum velocity threshold for momentum
    if (Math.abs(currentVelocity) < 0.5) return;

    const animate = () => {
      // Enhanced friction curve for more natural feel
      const friction = 0.92;
      currentVelocity *= friction;
      
      const newTranslateX = applyBoundaries(currentTranslateX + currentVelocity);
      
      setCurrentTranslateX(newTranslateX);
      setBaseTranslateX(newTranslateX);
      updateTransform(newTranslateX);

      // Continue animation if velocity is significant
      if (Math.abs(currentVelocity) > 0.1) {
        animationRef.current = requestAnimationFrame(animate);
      } else {
        setIsScrolling(false);
      }
    };

    setIsScrolling(true);
    animationRef.current = requestAnimationFrame(animate);
  };

  const handleDragStart = (event: React.TouchEvent | React.MouseEvent) => {
    // Cancel any ongoing momentum animation
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    setIsDragging(true);
    setIsScrolling(false);
    const clientX = getPositionX(event.nativeEvent);
    const currentTime = Date.now();
    
    setStartX(clientX);
    setLastMoveX(clientX);
    setLastMoveTime(currentTime);
    setVelocity(0);
    touchStartTimeRef.current = currentTime;
    velocityHistoryRef.current = [];
    
    // Remove transitions for immediate response
    if (containerRef.current) {
      containerRef.current.style.transition = 'none';
    }
    if (progressRef.current) {
      progressRef.current.style.transition = 'none';
    }

    // Prevent default touch behaviors
    if ('touches' in event.nativeEvent) {
      event.preventDefault();
    }
  };

  const handleDragMove = (event: TouchEvent | MouseEvent) => {
    if (!isDragging) return;
    
    // Prevent default scrolling and zooming
    event.preventDefault();
    
    const clientX = getPositionX(event);
    const currentTime = Date.now();
    const deltaX = clientX - startX;
    const newTranslateX = baseTranslateX + deltaX;
    
    // Calculate instantaneous velocity
    const timeDelta = currentTime - lastMoveTime;
    if (timeDelta > 0) {
      const moveDelta = clientX - lastMoveX;
      const instantVelocity = moveDelta / timeDelta * 16; // Convert to pixels per frame (60fps)
      
      // Store velocity history for better momentum calculation
      velocityHistoryRef.current.push({
        velocity: instantVelocity,
        time: currentTime
      });
      
      // Keep only recent velocity samples (last 100ms)
      velocityHistoryRef.current = velocityHistoryRef.current.filter(
        item => currentTime - item.time < 100
      );
      
      setVelocity(instantVelocity);
    }
    
    setLastMoveX(clientX);
    setLastMoveTime(currentTime);
    
    // Apply boundaries with enhanced elastic effect
    let boundedTranslate = newTranslateX;
    const maxTranslate = 100;
    const minTranslate = -(modules.length * totalWidth) - 100;
    
    if (newTranslateX > maxTranslate) {
      // Stronger elastic resistance at boundaries
      const overscroll = newTranslateX - maxTranslate;
      boundedTranslate = maxTranslate + overscroll * 0.25;
    } else if (newTranslateX < minTranslate) {
      const overscroll = newTranslateX - minTranslate;
      boundedTranslate = minTranslate + overscroll * 0.25;
    }
    
    setCurrentTranslateX(boundedTranslate);
    updateTransform(boundedTranslate);
  };

  const handleDragEnd = () => {
    if (!isDragging) return;
    
    setIsDragging(false);
    
    // Calculate final velocity from recent movement
    const finalVelocity = calculateAverageVelocity();
    
    // Apply final boundaries with smooth spring-back
    const boundedTranslateX = applyBoundaries(currentTranslateX);
    
    if (boundedTranslateX !== currentTranslateX) {
      // Smooth spring-back animation to boundaries
      setCurrentTranslateX(boundedTranslateX);
      setBaseTranslateX(boundedTranslateX);
      
      if (containerRef.current) {
        containerRef.current.style.transition = 'transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
      }
      if (progressRef.current) {
        progressRef.current.style.transition = 'transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
      }
      updateTransform(boundedTranslateX);
      
      // Clear transition after animation
      setTimeout(() => {
        if (containerRef.current) {
          containerRef.current.style.transition = 'none';
        }
        if (progressRef.current) {
          progressRef.current.style.transition = 'none';
        }
      }, 500);
    } else {
      // Start momentum animation with calculated velocity
      setBaseTranslateX(currentTranslateX);
      startMomentumAnimation(finalVelocity);
    }
    
    // Clear velocity history
    velocityHistoryRef.current = [];
  };

  const navigateLeft = () => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    const newTranslateX = Math.min(baseTranslateX + totalWidth, 0);
    setBaseTranslateX(newTranslateX);
    setCurrentTranslateX(newTranslateX);
    
    if (containerRef.current) {
      containerRef.current.style.transition = 'transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    }
    if (progressRef.current) {
      progressRef.current.style.transition = 'transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    }
    updateTransform(newTranslateX);
    
    // Clear transition after animation
    setTimeout(() => {
      if (containerRef.current) {
        containerRef.current.style.transition = 'none';
      }
      if (progressRef.current) {
        progressRef.current.style.transition = 'none';
      }
    }, 400);
  };

  const navigateRight = () => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    const maxTranslate = -(modules.length - 1) * totalWidth;
    const newTranslateX = Math.max(baseTranslateX - totalWidth, maxTranslate);
    setBaseTranslateX(newTranslateX);
    setCurrentTranslateX(newTranslateX);
    
    if (containerRef.current) {
      containerRef.current.style.transition = 'transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    }
    if (progressRef.current) {
      progressRef.current.style.transition = 'transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    }
    updateTransform(newTranslateX);
    
    // Clear transition after animation
    setTimeout(() => {
      if (containerRef.current) {
        containerRef.current.style.transition = 'none';
      }
      if (progressRef.current) {
        progressRef.current.style.transition = 'none';
      }
    }, 400);
  };

  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => handleDragMove(event);
    const handleTouchMove = (event: TouchEvent) => handleDragMove(event);
    const handleMouseUp = () => handleDragEnd();
    const handleTouchEnd = () => handleDragEnd();

    if (isDragging) {
      // Use passive: false to allow preventDefault
      document.addEventListener('mousemove', handleMouseMove, { passive: false });
      document.addEventListener('mouseup', handleMouseUp, { passive: false });
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd, { passive: false });
      
      // Prevent context menu on long press
      document.addEventListener('contextmenu', (e) => e.preventDefault(), { passive: false });
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
      document.removeEventListener('contextmenu', (e) => e.preventDefault());
    };
  }, [isDragging, startX, baseTranslateX, velocity, lastMoveTime, lastMoveX]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      updateTransform(currentTranslateX);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [currentTranslateX]);

  // Cleanup animation on unmount
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  const isLandscape = window.innerHeight < window.innerWidth && window.innerWidth < 1024;
  const isMobile = window.innerWidth < 768;
  const isMobileLandscape = isLandscape && isMobile;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white overflow-hidden">
      {/* Fixed Header */}
      <div className={`fixed top-0 left-0 right-0 z-30 px-4 ${isMobileLandscape ? 'py-3' : 'py-6'} sm:px-6 lg:px-8 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900`}>
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <button className={`flex items-center gap-2 px-3 ${isMobileLandscape ? 'py-1.5' : 'py-2'} bg-slate-700/50 hover:bg-slate-700/70 rounded-xl backdrop-blur-sm transition-all duration-200 hover:scale-105`}>
            <ChevronLeft className={`${isMobileLandscape ? 'w-4 h-4' : 'w-5 h-5'}`} />
            <span className={`${isMobileLandscape ? 'text-xs' : 'text-sm'} font-medium`}>Back</span>
          </button>
          
          <h1 className={`${isMobileLandscape ? 'text-lg' : 'text-xl sm:text-2xl lg:text-3xl'} font-bold text-center`}>
            GMP & Regulations
          </h1>
          
          <div className={`${isMobileLandscape ? 'w-12' : 'w-16 sm:w-20'}`}></div>
        </div>
      </div>

      {/* Progress Indicators that Move with Cards */}
      <div className={`fixed ${isMobileLandscape ? 'top-16' : 'top-20 sm:top-24 lg:top-28'} left-0 right-0 z-20 px-4 sm:px-6 lg:px-8 pointer-events-none overflow-hidden`}>
        <div className="max-w-7xl mx-auto">
          <div 
            ref={progressRef}
            className="relative flex items-center will-change-transform"
            style={{ 
              transform: `translateX(${currentTranslateX}px)`,
              width: `${modules.length * totalWidth}px`,
              gap: `${cardGap}px`
            }}
          >
            {/* Progress Line that spans across all cards */}
            <div className="absolute top-1/2 left-0 right-0 h-1 bg-slate-700 rounded-full transform -translate-y-1/2">
              <div className="h-full bg-gradient-to-r from-pink-500 via-orange-500 via-teal-500 to-purple-500 rounded-full opacity-30" />
            </div>
            
            {/* Step Indicators that move with cards */}
            {modules.map((module, index) => (
              <div 
                key={module.id}
                className={`${isMobileLandscape ? 'w-6 h-6 text-xs' : 'w-8 h-8 sm:w-10 sm:h-10 text-sm'} rounded-full flex items-center justify-center font-bold border-2 bg-slate-700 border-slate-500 text-slate-300 relative z-10 flex-none`}
                style={{ 
                  width: `${cardWidth}px`,
                  marginRight: index < modules.length - 1 ? `${cardGap}px` : '0',
                  display: 'flex',
                  justifyContent: 'center'
                }}
              >
                <div className={`${isMobileLandscape ? 'w-6 h-6 text-xs' : 'w-8 h-8 sm:w-10 sm:h-10 text-sm'} rounded-full flex items-center justify-center font-bold border-2 bg-slate-700 border-slate-500 text-slate-300`}>
                  {module.id}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Cards Container with Navigation */}
      <div className={`relative ${isMobileLandscape ? 'pt-28 pb-4' : 'pt-36 sm:pt-40 lg:pt-44 pb-8'} px-4 sm:px-6 lg:px-8`}>
        <div className="max-w-7xl mx-auto">
          {/* Navigation Buttons */}
          <div className="absolute inset-y-0 left-0 right-0 flex items-center justify-between pointer-events-none z-20">
            <button
              onClick={navigateLeft}
              className={`${isMobileLandscape ? 'w-8 h-8 ml-2' : 'w-12 h-12 ml-4'} bg-slate-800/80 hover:bg-slate-700/90 rounded-full flex items-center justify-center backdrop-blur-sm transition-all duration-200 hover:scale-110 pointer-events-auto shadow-lg border border-slate-600/50`}
            >
              <ChevronLeft className={`${isMobileLandscape ? 'w-4 h-4' : 'w-6 h-6'} text-white`} />
            </button>
            
            <button
              onClick={navigateRight}
              className={`${isMobileLandscape ? 'w-8 h-8 mr-2' : 'w-12 h-12 mr-4'} bg-slate-800/80 hover:bg-slate-700/90 rounded-full flex items-center justify-center backdrop-blur-sm transition-all duration-200 hover:scale-110 pointer-events-auto shadow-lg border border-slate-600/50`}
            >
              <ChevronRight className={`${isMobileLandscape ? 'w-4 h-4' : 'w-6 h-6'} text-white`} />
            </button>
          </div>

          <div className="overflow-hidden">
            <div 
              ref={containerRef}
              className={`flex will-change-transform touch-pan-x select-none ${
                isDragging ? 'cursor-grabbing' : 'cursor-grab'
              } ${isScrolling ? 'pointer-events-none' : ''}`}
              style={{ 
                gap: `${cardGap}px`,
                width: `${modules.length * totalWidth}px`,
                transform: `translateX(${currentTranslateX}px)`,
                touchAction: 'pan-x',
                WebkitUserSelect: 'none',
                userSelect: 'none'
              }}
              onMouseDown={handleDragStart}
              onTouchStart={handleDragStart}
            >
              {modules.map((module, index) => (
                <div
                  key={module.id}
                  className={`flex-none rounded-2xl bg-gradient-to-br ${module.bgGradient} p-4 ${isMobileLandscape ? 'p-3' : 'p-6'} shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 select-none`}
                  style={{ 
                    width: `${cardWidth}px`,
                    height: isMobileLandscape ? '240px' : isMobile ? '320px' : '384px',
                    userSelect: 'none',
                    WebkitUserSelect: 'none',
                    WebkitTouchCallout: 'none'
                  }}
                >
                  <div className="flex flex-col h-full">
                    {/* Step Label */}
                    <div className={`inline-flex items-center gap-2 ${isMobileLandscape ? 'mb-2' : 'mb-4'}`}>
                      <div className="bg-white/20 backdrop-blur-sm rounded-full px-3 py-1">
                        <span className={`${isMobileLandscape ? 'text-xs' : 'text-sm'} font-medium text-white`}>{module.step}</span>
                      </div>
                    </div>

                    {/* Title */}
                    <h2 className={`${isMobileLandscape ? 'text-lg mb-3' : 'text-2xl sm:text-3xl mb-6'} font-bold ${module.color} leading-tight`}>
                      {module.title}
                    </h2>

                    {/* Level Badge */}
                    <div className={`flex items-center gap-3 ${isMobileLandscape ? 'mb-2' : 'mb-4'}`}>
                      <span className={`px-3 py-1 ${module.levelColor} rounded-full text-white ${isMobileLandscape ? 'text-xs' : 'text-sm'} font-medium`}>
                        {module.level}
                      </span>
                      <div className="flex items-center gap-1">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className={`${isMobileLandscape ? 'w-3 h-3' : 'w-4 h-4'} bg-yellow-400 rounded-full`} />
                        ))}
                      </div>
                    </div>

                    {/* Spacer */}
                    <div className="flex-1"></div>

                    {/* Bottom Info */}
                    <div className={`bg-black/20 backdrop-blur-sm rounded-xl ${isMobileLandscape ? 'p-3' : 'p-4'} mt-auto`}>
                      <div className="flex items-center justify-between">
                        <span className={`text-white/90 ${isMobileLandscape ? 'text-xs' : 'text-sm'}`}>
                          Bloom's: {module.bloomsLevel}
                        </span>
                        <span className={`text-white font-semibold ${isMobileLandscape ? 'text-xs' : 'text-sm'}`}>
                          {module.duration}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export {LevelList};
